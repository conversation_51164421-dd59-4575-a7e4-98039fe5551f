#!/usr/bin/env python3
"""
Thermal Label Generator for Wildbreed Inventory
Generates 4x6 thermal labels from CSV inventory data
"""

import pandas as pd
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import inch
from reportlab.pdfgen import canvas
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.lib import colors
import argparse
import sys
from pathlib import Path


class ThermalLabelGenerator:
    def __init__(self, csv_file: str, output_file: str = "thermal_labels.pdf"):
        self.csv_file = csv_file
        self.output_file = output_file
        
        # 4x6 inch label dimensions
        self.label_width = 4 * inch
        self.label_height = 6 * inch
        
        # Margins and spacing
        self.margin = 0.25 * inch
        self.line_spacing = 0.3 * inch
        
        # Font sizes
        self.main_font_size = 14
        self.sku_font_size = 12
        
    def load_data(self):
        """Load and validate CSV data"""
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"Loaded {len(self.df)} rows from {self.csv_file}")
            
            # Validate required columns
            required_columns = ['Style', 'Color', 'Size', 'Count', 'SKU']
            missing_columns = [col for col in required_columns if col not in self.df.columns]
            
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
                
            # Clean data - handle missing values
            self.df['Style'] = self.df['Style'].fillna('Unknown')
            self.df['Color'] = self.df['Color'].fillna('Unknown')
            self.df['Size'] = self.df['Size'].fillna('Unknown')
            self.df['Count'] = pd.to_numeric(self.df['Count'], errors='coerce').fillna(0).astype(int)
            self.df['SKU'] = self.df['SKU'].fillna('Unknown')
            
            return True
            
        except Exception as e:
            print(f"Error loading CSV file: {e}")
            return False
    
    def generate_label_data(self):
        """Generate individual label data based on count"""
        labels = []
        
        for _, row in self.df.iterrows():
            style = row['Style']
            color = row['Color']
            size = row['Size']
            count = int(row['Count'])
            sku = row['SKU']
            
            # Create labels based on count
            for i in range(count):
                label_data = {
                    'line1': f"{style} - {color} - {size}",
                    'line2': sku
                }
                labels.append(label_data)
        
        print(f"Generated {len(labels)} individual labels")
        return labels
    
    def create_pdf(self, labels):
        """Create PDF with thermal labels"""
        # Calculate labels per page (assuming 2x2 grid on letter size)
        page_width, page_height = letter
        labels_per_row = int(page_width // self.label_width)
        labels_per_col = int(page_height // self.label_height)
        labels_per_page = labels_per_row * labels_per_col
        
        # For 4x6 labels on letter size, we'll do 2x1 (2 labels per page)
        if labels_per_page == 0:
            labels_per_page = 1  # At least one label per page
        
        c = canvas.Canvas(self.output_file, pagesize=letter)
        
        for i, label in enumerate(labels):
            # Calculate position on page
            page_num = i // labels_per_page
            label_on_page = i % labels_per_page
            
            # For 4x6 labels, we'll center them on the page
            x_offset = (page_width - self.label_width) / 2
            y_offset = page_height - self.label_height - (page_height - self.label_height) / 2
            
            # If multiple labels per page, adjust positioning
            if labels_per_page > 1:
                row = label_on_page // labels_per_row
                col = label_on_page % labels_per_row
                x_offset = col * self.label_width + (page_width - labels_per_row * self.label_width) / 2
                y_offset = page_height - (row + 1) * self.label_height - (page_height - labels_per_col * self.label_height) / 2
            
            self.draw_label(c, label, x_offset, y_offset)
            
            # Start new page if needed
            if (i + 1) % labels_per_page == 0 and i < len(labels) - 1:
                c.showPage()
        
        c.save()
        print(f"PDF saved as {self.output_file}")
    
    def draw_label(self, canvas, label_data, x_offset, y_offset):
        """Draw a single label on the canvas"""
        # Draw border (optional - remove for actual thermal printing)
        canvas.setStrokeColor(colors.lightgrey)
        canvas.setLineWidth(0.5)
        canvas.rect(x_offset, y_offset, self.label_width, self.label_height)
        
        # Set text properties
        canvas.setFillColor(colors.black)
        
        # Calculate text positions
        text_x = x_offset + self.margin
        text_y_start = y_offset + self.label_height - self.margin - 0.5 * inch
        
        # Draw first line (Style - Color - Size)
        canvas.setFont("Helvetica-Bold", self.main_font_size)
        canvas.drawString(text_x, text_y_start, label_data['line1'])
        
        # Draw second line (SKU)
        canvas.setFont("Helvetica", self.sku_font_size)
        canvas.drawString(text_x, text_y_start - self.line_spacing, label_data['line2'])
    
    def generate_labels(self):
        """Main method to generate thermal labels"""
        print("Starting thermal label generation...")
        
        if not self.load_data():
            return False
        
        labels = self.generate_label_data()
        if not labels:
            print("No labels to generate")
            return False
        
        self.create_pdf(labels)
        print(f"Successfully generated {len(labels)} labels in {self.output_file}")
        return True


def main():
    parser = argparse.ArgumentParser(description="Generate thermal labels from CSV inventory data")
    parser.add_argument("csv_file", help="Path to CSV file with inventory data")
    parser.add_argument("-o", "--output", default="thermal_labels.pdf", 
                       help="Output PDF file name (default: thermal_labels.pdf)")
    
    args = parser.parse_args()
    
    # Check if CSV file exists
    if not Path(args.csv_file).exists():
        print(f"Error: CSV file '{args.csv_file}' not found")
        sys.exit(1)
    
    # Generate labels
    generator = ThermalLabelGenerator(args.csv_file, args.output)
    success = generator.generate_labels()
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
