#!/usr/bin/env python3
"""
Thermal Label Generator for Wildbreed Inventory
Generates 4x6 thermal labels from CSV inventory data
"""

import pandas as pd
from reportlab.lib.pagesizes import letter
from reportlab.lib.units import inch
from reportlab.pdfgen import canvas
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.lib import colors
import argparse
import sys
from pathlib import Path


class ThermalLabelGenerator:
    def __init__(self, csv_file: str, output_file: str = "thermal_labels.pdf"):
        self.csv_file = csv_file
        self.output_file = output_file

        # 4x6 inch page dimensions
        self.page_width = 4 * inch
        self.page_height = 6 * inch

        # 6 labels per page (6 rows x 1 column) - vertically stacked
        self.labels_per_row = 1
        self.labels_per_col = 6
        self.labels_per_page = 6

        # Individual label dimensions - full width, 1/6 height
        self.label_width = self.page_width  # Full 4-inch width
        self.label_height = self.page_height / self.labels_per_col  # 1 inch height each

        # Margins and spacing (adjusted for 1-inch tall labels)
        self.margin = 0.1 * inch
        self.line_spacing = 0.2 * inch

        # Font sizes (larger since we have more vertical space)
        self.main_font_size = 12
        self.sku_font_size = 10

        # Logo settings
        self.logo_file = "wild-breed-logo-v2-square-black.png"
        self.logo_size = 0.6 * inch  # Larger logo for full-width labels
        
    def load_data(self):
        """Load and validate CSV data"""
        try:
            self.df = pd.read_csv(self.csv_file)
            print(f"Loaded {len(self.df)} rows from {self.csv_file}")
            
            # Validate required columns
            required_columns = ['Style', 'Color', 'Size', 'Count', 'SKU_BASE']
            missing_columns = [col for col in required_columns if col not in self.df.columns]

            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")

            # Clean data - handle missing values
            self.df['Style'] = self.df['Style'].fillna('Unknown')
            self.df['Color'] = self.df['Color'].fillna('Unknown')
            self.df['Size'] = self.df['Size'].fillna('Unknown')
            self.df['Count'] = pd.to_numeric(self.df['Count'], errors='coerce').fillna(0).astype(int)
            self.df['SKU_BASE'] = self.df['SKU_BASE'].fillna('Unknown')
            
            return True
            
        except Exception as e:
            print(f"Error loading CSV file: {e}")
            return False
    
    def generate_label_data(self):
        """Generate individual label data based on count with index numbers"""
        labels = []

        for _, row in self.df.iterrows():
            style = row['Style']
            color = row['Color']
            size = row['Size']
            count = int(row['Count'])
            sku_base = row['SKU_BASE']

            # Create labels based on count with index numbers
            for i in range(count):
                # Add index number to SKU_BASE (1-based indexing)
                indexed_sku = f"{sku_base}-{i+1:03d}"  # Format as 001, 002, etc.

                label_data = {
                    'line1': f"{style} - {color} - {size}",
                    'line2': indexed_sku
                }
                labels.append(label_data)

        print(f"Generated {len(labels)} individual labels with index numbers")
        return labels
    
    def create_pdf(self, labels):
        """Create PDF with thermal labels - 6 vertically stacked labels per 4x6 page"""
        # Use 4x6 page size
        page_size = (self.page_width, self.page_height)
        c = canvas.Canvas(self.output_file, pagesize=page_size)

        for i, label in enumerate(labels):
            # Calculate position on page (6 rows x 1 column - vertically stacked)
            label_on_page = i % self.labels_per_page
            row = label_on_page  # Each label is its own row

            # Calculate x,y position for this label
            x_offset = 0  # Always start at left edge (full width)
            y_offset = self.page_height - (row + 1) * self.label_height

            self.draw_label(c, label, x_offset, y_offset)

            # Start new page if needed
            if (i + 1) % self.labels_per_page == 0 and i < len(labels) - 1:
                c.showPage()

        c.save()
        print(f"PDF saved as {self.output_file}")
    
    def draw_label(self, canvas, label_data, x_offset, y_offset):
        """Draw a single label on the canvas with logo"""
        # Draw border (optional - remove for actual thermal printing)
        canvas.setStrokeColor(colors.lightgrey)
        canvas.setLineWidth(0.5)
        canvas.rect(x_offset, y_offset, self.label_width, self.label_height)

        # Set text properties
        canvas.setFillColor(colors.black)

        # Try to draw logo on the left side, centered vertically
        logo_x = x_offset + self.margin
        logo_y = y_offset + (self.label_height - self.logo_size) / 2

        try:
            from pathlib import Path
            if Path(self.logo_file).exists():
                canvas.drawImage(self.logo_file, logo_x, logo_y,
                               width=self.logo_size, height=self.logo_size,
                               preserveAspectRatio=True, mask='auto')
        except Exception as e:
            print(f"Warning: Could not load logo {self.logo_file}: {e}")

        # Calculate text positions (offset to the right of logo, centered vertically)
        text_x = x_offset + self.margin + self.logo_size + 0.1 * inch
        text_y_center = y_offset + self.label_height / 2
        text_y_start = text_y_center + self.line_spacing / 2

        # Draw first line (Style - Color - Size)
        canvas.setFont("Helvetica-Bold", self.main_font_size)
        canvas.drawString(text_x, text_y_start, label_data['line1'])

        # Draw second line (SKU_BASE)
        canvas.setFont("Helvetica", self.sku_font_size)
        canvas.drawString(text_x, text_y_start - self.line_spacing, label_data['line2'])
    
    def generate_labels(self):
        """Main method to generate thermal labels"""
        print("Starting thermal label generation...")
        
        if not self.load_data():
            return False
        
        labels = self.generate_label_data()
        if not labels:
            print("No labels to generate")
            return False
        
        self.create_pdf(labels)
        print(f"Successfully generated {len(labels)} labels in {self.output_file}")
        return True


def main():
    parser = argparse.ArgumentParser(description="Generate thermal labels from CSV inventory data")
    parser.add_argument("csv_file", help="Path to CSV file with inventory data")
    parser.add_argument("-o", "--output", default="thermal_labels.pdf", 
                       help="Output PDF file name (default: thermal_labels.pdf)")
    
    args = parser.parse_args()
    
    # Check if CSV file exists
    if not Path(args.csv_file).exists():
        print(f"Error: CSV file '{args.csv_file}' not found")
        sys.exit(1)
    
    # Generate labels
    generator = ThermalLabelGenerator(args.csv_file, args.output)
    success = generator.generate_labels()
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
