# Wildbreed Thermal Label Generator

Generate thermal labels for inventory from CSV data, formatted for 4x6 thermal label printing.

## Features

- Reads CSV inventory data
- Generates labels with format:
  - Line 1: `<Style> - <Color> - <Size>`
  - Line 2: `<SKU>`
- Repeats labels based on `Count` field
- Outputs PDF optimized for 4x6 thermal labels

## Quick Start

### Generate labels from the default CSV file

```bash
uv run python generate_labels.py
```

### Generate labels from a custom CSV file

```bash
uv run python label_generator.py "your_file.csv"
```

### Generate labels with custom output filename

```bash
uv run python label_generator.py "your_file.csv" -o "custom_labels.pdf"
```

## CSV Format

Your CSV file should have these columns:

- `Style`: Product style name
- `Color`: Product color
- `Size`: Product size
- `Count`: Number of labels to generate for this item
- `SKU`: Product SKU code

Example:

```csv
Style,Color,Size,Count,SKU
Solana,Salmon,M,14,HAT-SOLANA-SALM-M-001
Encinitas,Bone,M,3,HAT-ENCINI-BON-M-001
```

## Installation

Install dependencies:

```bash
uv add pandas reportlab
```

## Output

The generated PDF contains thermal labels ready for printing on 4x6 label stock. Each label shows:

- Product information on the first line
- SKU on the second line
- Multiple copies based on the Count field

## Files

- `label_generator.py`: Main label generation script with full options
- `generate_labels.py`: Simple script for quick label generation
- `thermal_labels.pdf`: Generated output file (created after running)

## Usage Examples

Generate 201 labels from the inventory CSV:

```bash
uv run python generate_labels.py
```

This will create `thermal_labels.pdf` with all labels ready for thermal printing.
