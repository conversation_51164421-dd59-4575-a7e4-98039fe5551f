# Wildbreed Thermal Label Generator

Generate thermal labels for inventory from CSV data, formatted for 4x6 thermal label printing.

## Features

- Reads CSV inventory data
- Generates **6 labels per 4x6 page** for efficient thermal printing
- Each label includes:
  - **Wildbreed logo** on the left side
  - Line 1: `<Style> - <Color> - <Size>`
  - Line 2: `<SKU_BASE>`
- Repeats labels based on `Count` field
- Outputs PDF optimized for 4x6 thermal label sheets

## Quick Start

### Generate labels from the default CSV file

```bash
uv run python generate_labels.py
```

### Generate labels from a custom CSV file

```bash
uv run python label_generator.py "your_file.csv"
```

### Generate labels with custom output filename

```bash
uv run python label_generator.py "your_file.csv" -o "custom_labels.pdf"
```

## CSV Format

Your CSV file should have these columns:

- `Style`: Product style name
- `Color`: Product color
- `Size`: Product size
- `Count`: Number of labels to generate for this item
- `SKU_BASE`: Product SKU base code

Example:

```csv
Style,Color,Size,Count,SKU_BASE
Solana,Salmon,M,14,HAT-SOLANA-SALM-M
Encinitas,Bone,M,3,HAT-ENCINI-BON-M
```

## Installation

Install dependencies:

```bash
uv add pandas reportlab
```

## Output

The generated PDF contains thermal labels ready for printing on 4x6 label stock with **6 labels per page** (3 rows × 2 columns). Each label shows:

- **Wildbreed logo** on the left side
- Product information on the first line: `Style - Color - Size`
- SKU_BASE on the second line
- Multiple copies based on the Count field

## Logo Requirements

Place `wild-breed-logo-v2-square-black.png` in the same directory as the scripts. The logo will be automatically included on the left side of each label.

## Files

- `label_generator.py`: Main label generation script with full options
- `generate_labels.py`: Simple script for quick label generation
- `thermal_labels.pdf`: Generated output file (created after running)

## Usage Examples

Generate 201 labels from the inventory CSV (6 labels per 4x6 page):

```bash
uv run python generate_labels.py
```

This will create `thermal_labels.pdf` with all labels ready for thermal printing. The PDF will contain approximately 34 pages (201 labels ÷ 6 labels per page).
