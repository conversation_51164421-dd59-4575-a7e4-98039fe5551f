#!/usr/bin/env python3
"""
Simple script to generate labels from the default CSV file
"""

from label_generator import ThermalLabelGenerator
import sys
from pathlib import Path

def main():
    # Default CSV file name
    csv_file = "Wildbreed Inventory Prep - Labeling - All_Hat_SKUs__Full_Inventory_.csv"
    output_file = "thermal_labels.pdf"
    
    # Check if CSV file exists
    if not Path(csv_file).exists():
        print(f"Error: CSV file '{csv_file}' not found")
        print("Make sure the CSV file is in the current directory")
        sys.exit(1)
    
    # Generate labels
    print("🏷️  Generating thermal labels...")
    generator = ThermalLabelGenerator(csv_file, output_file)
    success = generator.generate_labels()
    
    if success:
        print(f"✅ Labels generated successfully!")
        print(f"📄 Output file: {output_file}")
        print(f"🖨️  Ready for 4x6 thermal printing (6 vertically stacked labels per page)")
        print(f"🏷️  Each label: Full 4-inch width × 1-inch height")
        print(f"📝 Format: Style-Color-Size and SKU_BASE-### (with index numbers)")
        print(f"🎨 Logo included on left side of each label")
    else:
        print("❌ Failed to generate labels")
        sys.exit(1)

if __name__ == "__main__":
    main()
